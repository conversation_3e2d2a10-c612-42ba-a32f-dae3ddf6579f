{%- liquid
  assign se_stts = section.settings
  assign se_blocks = section.blocks 
  assign admin_sp = request.design_mode
  assign h__bgimg = se_stts.h__bgimg
  assign page_type = request.page_type
  if page_type == 'index' and se_stts.transparent_header 
    assign h_transparent = true 
  elsif page_type == 'collection' and se_stts.transparent_collection
    assign h_transparent = true 
  elsif page_type == 'article' and se_stts.transparent_article and article.image != blank
    assign h_transparent = true 
  elsif page_type == 'blog' and se_stts.transparent_blog
    assign h_transparent = true 
  else
    assign h_transparent = false 
  endif
  assign space_transparent = se_stts.space_transparent_header 
  assign source = se_stts.source 
  assign padding_section = se_stts.pd | remove: ' ' | split: ',' -%}

<div data-header-options='{ "isTransparent": {{ h_transparent }},"isSticky": {{ se_stts.sticky_header }},"hideScroldown": {{ se_stts.scroll_header }}}' class="bee-header__wrapper bee-layout-layout_{{ se_stts.layout }} {% if h__bgimg != blank %} lazyloadbee bee-header__bgimg" data-bgset="{{ h__bgimg | image_url: width: 1 }}" data-ratio="{{ h__bgimg.aspect_ratio }}" data-sizes="auto"{% else %}"{% endif %} {%- if se_stts.full_header -%} style="--pd-top: {{ padding_section[0] | default: "0px" }};--pd-right: {{ padding_section[1] | default: "1.5rem" }};--pd-bottom: {{ padding_section[2] | default: "0px" }};--pd-left: {{ padding_section[3] | default: "1.5rem" }};"{% endif %}>
  <div class="bee-container">
    <div data-header-height class="bee-row bee-gx-15 bee-gx-md-30 bee-align-items-center">
    {%- case se_stts.layout -%}

      {%- when 'menu_left' -%}
        <div class="bee-col-3 bee-col-item">{%- render 'push_menu' -%}</div>
        <div class="bee-col-6 bee-text-center bee-col-item">{%- render 'bee_logo', tag: 'div', isTransparent: h_transparent -%}</div>
        <div class="bee-col-3 bee-text-end bee-col-group_btns bee-col-item bee-lh-1">{%- render 'bee_group_btns', se_stts: se_stts, isfrmSearch: false -%}</div>
    
      {%- when 'menu_right' -%}
        {%- style -%}
          @media (min-width: 1025px) {
            #bee-menu-drawer,#bee-menu-drawer + .bee-drawer-menu__close {
                right: 0;
                left: auto;
                -webkit-transform: translate3d(104%,0,0);
                transform: translate3d(104%,0,0)
            }
            #bee-menu-drawer + .bee-drawer-menu__close {
                right: 350px;
                left:auto
            }
            .rtl_true #bee-menu-drawer,.bee-drawer-menu__close {
                right: auto;
                left: 0;
                -webkit-transform: translate3d(-104%,0,0);
                transform: translate3d(-104%,0,0)
            }
            .rtl_true #bee-menu-drawer + .bee-drawer-menu__close {
                left: 350px;
                right:auto
            }
            #bee-menu-drawer[aria-hidden=false],
            .rtl_true #bee-menu-drawer[aria-hidden=false]{
                pointer-events: auto;
                visibility: visible;
                transform: none;
                -webkit-transform: none;
            }
            #bee-menu-drawer[aria-hidden=false]+.bee-drawer-menu__close,
            .rtl_true #bee-menu-drawer[aria-hidden=false]+.bee-drawer-menu__close {
                transform: none;
                -webkit-transform: none;
            }
          }
        {%- endstyle -%}
        <div class="bee-d-lg-none bee-col-3 bee-col-item">{%- render 'push_menu' -%}</div>
        <div class="bee-d-none bee-d-lg-block bee-col-3 bee-col__textSocial bee-col-item">
            {%- if source == '1' -%}{{- se_stts.txt -}}
            {%- elsif source == '2' or source == '3' -%}
              {{ 'bee-icon-social.css' | asset_url | stylesheet_tag }}
              {%- if source == '3' %}{% assign follow_social = true %}{% endif -%}
              {%- render 'social_sharing', style: 'simple', use_color_set: false, size: 'medium', space_h_item: 20, space_h_item_mb: 20, space_v_item: 0, space_v_item_mb: 0, share_permalink: shop.url, share_title: shop.name, follow_social: follow_social -%}
            {%- endif -%}
        </div>
        <div class="bee-col-6 bee-text-center bee-col-item">{%- render 'bee_logo', tag:'div', isTransparent: h_transparent -%}</div>
        <div class="bee-col-3 bee-text-end bee-col-group_btns bee-col-item bee-lh-1">{%- render 'bee_group_btns', se_stts: se_stts, isShowMenuBtn: true, isfrmSearch: false -%}</div>

      {%- else -%}
        
        <div class="bee-col-auto bee-col-item">{%- render 'push_menu' -%}</div>
        <div class="bee-col-auto bee-text-center bee-text-lg-start bee-col-item">{%- render 'bee_logo', tag: 'div', isTransparent: h_transparent -%}</div>
        <div class="bee-d-none bee-d-lg-block bee-flex-fill bee-col-6 bee-col-item">
        <div data-predictive-search data-sid="search-hidden" class="bee-search-header__form-wrap">
          {%- style -%}
            .bee-section-header:not(.calc-pos-submenu) {
                overflow-x: visible;
            }
            .bee-search-header__form-wrap {
                max-width: {{se_stts.frm_sea_mw}}px;
            }
            .bee-search-header__form {
                padding: 0;
                border-radius: var(--btn-radius);
                padding: 2px;
                max-width: {{se_stts.frm_sea_mw}}px;
                margin: 0;    
                display: inline-flex;width: 100%;
                border: 2px solid transparent;
            }
            .bee-search-header__form-wrap:hover .bee-search-header__form{
              border-color: rgba(var(--h-text-color-rgb), 0.15);
            }
            .bee-search-header__form-wrap:focus .bee-search-header__form,
            .bee-search-header__form-wrap:vistited .bee-search-header__form,
            .bee-search-header__form-wrap:active .bee-search-header__form,
            .bee-search-header__form-wrap:focus-visible .bee-search-header__form {
                border-color: var(--h-text-color);
            }
            {% if se_stts.search_border %}
            .bee-search-header__form {
              border-color: rgba(var(--h-text-color-rgb), 0.15);
            }
            {% endif %}
            .bee-search-header__input {
                background-color: transparent;
                padding: 0 15px 0 40px;
                height: 40px;
                border: 0;
                width: 100%;
                line-height: 18px;
                color: var(--h-text-color);
                border-radius: var(--btn-radius);
                font-size: 15px;
            }
            .bee-search-header__input:focus::placeholder{color: transparent;}
            .bee-search-header__submit {
                --h-btn-color: var(--h-text-color);
                min-width: 40px;
                background-color: transparent;
                color: var(--h-btn-color);
                font-size: 14px;
                transition: color .25s ease, background-color .25s ease, border-color .25s ease, box-shadow .25s ease, opacity .25s ease;
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                height: 100%;
                padding: 12px 15px;
            }
            .bee-search-header__submit:hover {
                background-color: transparent;
                color: var(--h-text-color);
            }
            .bee-site-nav__icons .bee-search-header__submit svg.bee-icon {
                  color: rgba(var(--h-text-color-rgb), 0.15);
                  width: 15px;
                  height: 15px;
            }
            .bee-search-header__type select {
              border: 0;
              max-width:138px;
              padding: 0 30px 0 15px;
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
              font-size: 15px;
              display: inline-block;
              background-color: transparent;
              box-shadow: none;
              color: var(--h-text-color);
              background: none;
              line-height: 1.2;
              background: none;
          }
          .bee-search-header__type .bee-icon-select-arrow {
              fill: currentColor;
              width: 18px;
              height: 20px;
              color: rgba(var(--h-text-color-rgb), 0.4);
          }
          .bee-search-header__type select:hover + .bee-icon-select-arrow  {
            color: var(--h-text-color);
          }
            .bee-search-header_border {
                height: 20px;
                background-color: rgba(var(--h-text-color-rgb), 0.15);
                width: 1.5px;
            }
            .bee-section-header:not(.calc-pos-submenu) {
                overflow: visible !important;
            }
            .bee-search-header__form-wrap > .bee-pr{
                padding-top: 5px;
            }
            .bee-frm-search__results {
                position: absolute;
                top: 100%;
                right: 0;
                left: 0;
                z-index: 1000;
                width: auto;
                height: auto;
                background-color: var(--bee-light-color);
                opacity: 0;
                visibility: hidden;
                pointer-events: none;
                transition: all .1s ease-in-out;
                max-width: {{se_stts.frm_sea_mw}}px;
                box-shadow: 0 1px 5px 2px rgba(var(--border-color-rgb),.3);
            }
              .bee-search-header__form-wrap:hover .bee-frm-search__results {
                  opacity: 1;
                  visibility: visible;
                  pointer-events: auto;
              }
              .bee-frm-search__content { 
                height:auto;
                max-height: 60vh!important;
                overflow: auto;
                overflow-x: hidden;
                -webkit-overflow-scrolling: touch;
                padding: 20px;
              }
              .bee-frm-search__content:empty {
                  display: none;
              }
              .bee-frm-search__content .bee-widget_img_pr {
                  min-width: 95px;
                  max-width: 95px;
                  max-height: 120px;
              }
              .bee-frm-search__content > .bee-widget__pr:not(.bee-row) {
                  display: flex;
                  column-gap: 20px;
                  padding: 0;
              }
              .bee-frm-search__content > .bee-widget__pr:not(:last-child) {
                  margin-bottom: 15px;
              }
              .bee-frm-search__content > .bee-widget__pr:not(.bee-row) .bee-widget_img_pr {
                  max-width:75px;
                  min-width: 75px;
              }
              .bee-frm-search__content .bee-widget_img_pr>a {
                height: 100%;
              }
              .bee-frm-search__content .bee-widget_img_pr img {
                object-fit: contain;
                max-height: 120px;
              }
              .bee-frm-search__content .bee-row.bee-widget__pr {
                  --ts-gutter-x: 20px;flex-wrap: nowrap;
              }
              .bee-frm-search__content .bee-widget__pr .bee-widget__pr-title {
                  font-weight: 500;
                  line-height: 1.25;
                  font-size: 14px;
                  color: var(--secondary-color);
              }
              .bee-frm-search__content .bee-widget__pr-price {
                font-size: 14px;
                color: var(--secondary-price-color);
              }
              .bee-frm-search__content .bee-widget__pr-price ins {
                  color: var(--primary-price-color);
                  margin-left: 5px;
              }
              .rtl_true .bee-frm-search__content .bee-widget__pr-price ins {
                  margin-right: 5px;
                  margin-left: 0;
                  display: inline-block;
              }
              .bee-frm-search__content .bee-widget__pr .bee-widget__pr-price {
                  margin-top: 1.5px;
              }
              .bee-search-header__form-wrap .bee-mini-search__viewAll {
                padding: 12px 20px;
                color: var(--secondary-color);
                border-top: 1px solid rgba(var(--border-color-rgb),.35);
                box-shadow: 0 0 10px 0 rgba(var(--border-color-rgb),.35);
             }
             .bee-frm-search__content .bee-widget__pr .bee-widget__pr-title:hover,
             .bee-search-header__form-wrap .bee-mini-search__viewAll:hover {
                    color: var(--accent-color);
             }
             .bee-search-header__form-wrap .bee-skeleton_img {
              background: rgb(225,227,228);
              padding-bottom: 100%;
              width: 80px;
            }
            .bee-search-header__form-wrap .bee-skeleton_txt1, .bee-skeleton_txt2 {
              height: 10px;
              width: 100%;
              background: rgb(225,227,228);
              margin-bottom: 8px;
            }
            .bee-search-header__form-wrap .bee-skeleton_txt2 {
              width: 38%;
              margin-bottom: 0;
            }
            .bee-search-header__form-wrap .bee-skeleton_wrap {
              padding: 15px;
            }
            .bee-search-header__form-wrap .bee-frm-search__results .bee-space-item-inner:not(:last-child){
              margin-bottom: 7.5px;
              padding-bottom: 7.5px;
            }
            .bee-search-header__form-wrap .bee-mini-search__keys {
                margin: 0;
                padding: 12px 20px;
                line-height: 1.4;
                font-size: 14px;
                box-shadow: 0 0 10px 0 rgba(var(--border-color-rgb),.35);
            }
          {%- endstyle -%}
            {%- liquid
             assign collection = collections[settings.search_prs_suggest]
             assign limit = 5 
             assign show_search_suggest = settings.show_search_suggest
             if shop.types.size < 3
             assign shop_types = shop.types | join: ' ' | remove: ' '
             else
             assign shop_types = 'type_bee'
             endif -%}
            <form data-frm-search action="{{ routes.search_url }}" method="get" class="bee-search-header__form bee-row bee-g-0 bee-align-items-center" role="search">
              <input type="hidden" name="type" value="product">
              <input type="hidden" name="options[unavailable_products]" value="{{settings.unavailable_prs}}">
              <input type="hidden" name="options[prefix]" value="last">
              {%- if settings.filter_type_search and shop_types != blank  -%}
              <div data-cat-search class="bee-search-header__type bee-pr bee-oh bee-col-auto bee-col-item">
                 <select data-name="product_type" class="bee-truncate">
                   <option value="*">{{ 'search.general.all_categories' | t }}</option>
                   {%- for product_type in shop.types -%}{%- if product_type == blank %}{% continue-%}{% endif -%}<option value="{{ product_type }}"{% if search_pr_type == product_type %} selected="selected"{% endif %}>{{ product_type }}</option>{%- endfor -%}
                 </select>
                 <svg class="bee-icon-select-arrow bee-pe-none" role="presentation" width="10" height="10" viewBox="0 0 19 12"><use xlink:href="#bee-select-arrow"></use></svg>
              </div>
              <div class="bee-search-header_border bee-col-auto bee-col-item"></div>
             {%- endif -%}
              <div class="bee-search-header__main bee-pr bee-oh bee-d-flex bee-col bee-col-item">
                <input data-input-search class="bee-search-header__input bee-input__currentcolor" autocomplete="off" type="text" name="q" placeholder="{{ 'search.general.placeholder_products' | t }}">
                <button class="bee-search-header__submit{% if settings.ajax_search %} bee-pe-none{% endif %}" type="submit"><svg width="22px" height="22px" viewBox="0 0 22 22" style="fill: currentColor;"><g><path d="M7,14c-3.86,0-7-3.14-7-7s3.14-7,7-7s7,3.14,7,7S10.86,14,7,14z M7,2C4.24,2,2,4.24,2,7s2.24,5,5,5s5-2.24,5-5S9.76,2,7,2z"/></g><g><path d="M17,18c-0.26,0-0.51-0.1-0.71-0.29l-3-3c-0.39-0.39-0.39-1.02,0-1.41s1.02-0.39,1.41,0l3,3c0.39,0.39,0.39,1.02,0,1.41C17.51,17.9,17.26,18,17,18z"/></g></svg></button>
              </div>
            </form>
            <div class="bee-pr">
                 <div class="bee-pa bee-frm-search__results bee-text-start">
                    <div data-skeleton-search class="bee-skeleton_wrap bee-dn"> 
                       {%- for i in (1..4) -%}
                       <div class="bee-row bee-space-item-inner">
                          <div class="bee-col-auto bee-col-item bee-widget_img_pr"><div class="bee-skeleton_img"></div></div>
                          <div class="bee-col bee-col-item bee-widget_if_pr"><div class="bee-skeleton_txt1"></div><div class="bee-skeleton_txt2"></div></div>
                       </div>
                       {%- endfor -%}
                    </div>
                    {%- assign list_hotkey = settings.list_hotkey -%}
                    {%- if settings.show_search_hotkey and list_hotkey != blank -%}
                    {%- capture link_suggest -%}
                    {{ routes.search_url }}?type=product&options%5Bunavailable_products%5D={{ settings.unavailable_prs }}&options%5Bprefix%5D=last&q=
                    {%- endcapture -%}
                    {%- assign arr_keys = list_hotkey | replace: ' ,', ',' | replace: ', ', ',' | split: ',' -%}
                    {%- assign arr_keys2 = arr_keys | join: ', | nt' | split: ' | nt' -%}
                      <div data-listKey class="bee-mini-search__keys">
                        <span class="bee-mini-search__label">{{ 'search.general.quick_search' | t }}</span>
                        <ul class="bee-mini-search__listKey bee-d-inline-block">
                           {%- for key in arr_keys %}{% assign key_strip = key | strip -%}
                           <li class="bee-d-inline-block"><a data-key='{{ key_strip | escape }}' href="{{ link_suggest }}{{ key | strip | url_encode }}">{{ arr_keys2[forloop.index0] }} </a></li>
                           {% endfor -%}
                        </ul>
                      </div>
                    {%- endif -%}
                    <div data-results-search class="bee-frm-search__content bee_ratioadapt bee-current-scrollbar"{% if collection == blank or show_search_suggest == false %} style="display: none;"{% endif %}>
                        {%- if collection != blank and show_search_suggest -%}
                            {%- liquid  
                            assign pr_url = product.url | within: collection 
                            assign placeholder_img = settings.placeholder_img
                            assign price_varies_style = settings.price_varies_style
                            
                        -%}
                            {%- for product in collection.products limit: limit -%}
                              {%- render 'pr-loop-item', imgatt: '', product: product, pr_url: product.url, placeholder_img: placeholder_img, price_varies_style: price_varies_style -%}
                            {%- endfor -%}
                        {%- endif -%}
                    </div>
                    {%- if collection != blank and show_search_suggest -%}
                       {%- if collection.all_products_count > limit -%}
                          <div data-viewAll-search>
                             <a href="{{ collection.url }}" class="bee-mini-search__viewAll bee-d-block">{{ 'search.pagination.view_all' | t }} <svg width="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M 18.71875 6.78125 L 17.28125 8.21875 L 24.0625 15 L 4 15 L 4 17 L 24.0625 17 L 17.28125 23.78125 L 18.71875 25.21875 L 27.21875 16.71875 L 27.90625 16 L 27.21875 15.28125 Z"/></svg></a>
                          </div>
                       {%- endif -%}
                    {%- else -%}<div data-viewAll-search></div>
                    {%- endif -%}
                 </div>
            </div>
        </div>
        </div>
        <div class="bee-col-auto bee-flex-fill bee-text-end bee-col-group_btns bee-col-item bee-lh-1">{%- render 'bee_group_btns', se_stts: se_stts, isfrmSearch: false -%}</div>

    {%- endcase -%}
    </div>
  </div>
</div>

{%- style -%}
  {%- assign opnav  = se_stts.opnav | divided_by: 100.0 -%}
  .bee-header__wrapper {
    --h-text-color      : {{ se_stts.clnav }};
    --h-text-color-rgb  : {{ se_stts.clnav | color_to_rgb | remove:'rgb(' |remove:')' }};
    --h-text-color-hover: {{ se_stts.clnav_hover }};
    --h-bg-color        : {{ se_stts.bgnav | color_modify: 'alpha', opnav }};
    --h-border-w: {%- if se_stts.show_border %}1px{% else %}0px{% endif -%};
    --h-border: {{ se_stts.bd_cl }};
    border-bottom: var(--h-border-w) solid var(--h-border);
    background-color: var(--h-bg-color);
  }
  .bee-count-box {
    --h-count-bgcolor: {{ se_stts.bg_hc }};
    --h-count-color: {{ se_stts.cl_hc }};
  }

  {%- if h__bgimg != blank %}
  .bee-header__bgimg {
      background-size: cover;
      background-repeat: no-repeat;
  }
  {%- endif -%}

  {%- if h_transparent -%}

    .bee-section-header,#shopify-section-top-bar { --h-space-tr: {% if space_transparent %}30px{% else %}0px{% endif %} }

    {%- assign opnavtr  = se_stts.opnavtr | divided_by: 100.0 -%}
    .bee-header__wrapper {
      --h-text-color      : {{ se_stts.clnavtr }};
      --h-text-color-rgb  : {{ se_stts.clnavtr | color_to_rgb | remove:'rgb(' |remove:')' }};
      --h-text-color-hover: {{ se_stts.clnavtr_hover }};
      --h-bg-color        : {{ se_stts.bgnavtr | color_modify: 'alpha', opnavtr }};
      --h-border-rgb: {{ se_stts.bd_cl_tr | color_to_rgb | remove: 'rgb(' | remove: ')' }};
      --h-border-trans-op: {{ se_stts.bd_cl_op | divided_by: 100.0 }}; 
      --h-border-w-tr: {%- if se_stts.show_border_tr %}1px{% else %}0px{% endif -%};
      border-bottom: var(--h-border-w-tr) solid rgba( var(--h-border-rgb), var(--h-border-trans-op));
    }
    .bee-section-header {
        margin-top: var(--h-space-tr);
        margin-bottom: calc(-1 * (var(--header-height) + var(--h-space-tr)));
        position: relative;
        top: 0;
        z-index: 460;
    }

    {%- if space_transparent -%}
      .is--topbar-transparent.is--header-transparent #shopify-section-top-bar {
        margin-top: var(--h-space-tr);
      }
      #bee-hsticky__sentinel {
        bottom:calc(-1 * var(--h-space-tr));
      }
    {%- endif -%}

  {%- endif -%}

  {%- if se_stts.sticky_header -%}
  
    {%- unless se_stts.scroll_header -%}
    .bee-hsticky__ready .bee-section-header {
        position: sticky;      
        top: 0;
        z-index: 460;
    }
    .is-header--stuck .bee-section-header {
      -webkit-box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
      box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
    }
    {%- endunless -%}

    {%- assign opnavst  = se_stts.opnavst | divided_by: 100.0 -%}
    .is-header--stuck .bee-header__wrapper {
      --h-text-color      : {{ se_stts.clnavst }};
      --h-text-color-rgb  : {{ se_stts.clnavst | color_to_rgb | remove:'rgb(' |remove:')' }};
      --h-text-color-hover: {{ se_stts.clnavst_hover }};
      --h-bg-color        : {{ se_stts.bgnavst | color_modify: 'alpha', opnavst }};
    }
    .is-header--stuck .header__sticky-logo {
      display:block !important
    }
    .is-header--stuck .header__normal-logo,
    .is-header--stuck .header__mobile-logo {
      display:none !important
    }
  {%- endif -%}
 
  .bee-section-header [data-header-height] {
      min-height: {{se_stts.h_navmb}}px;    
  }
  .bee-header__logo img {
    padding-top: 5px;
    padding-bottom: 5px;
    transform: translateZ(0);
    max-height: inherit;
    height: auto;
    width: 100%;
    max-width: 100%;
  }
  .bee-header__logo img[src*=".svg"] {
    height: 100%;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 800px;
  }
  .bee-site-nav__icons .bee-site-nav__icon {
      padding: 0 6px;
      display: inline-block;
      line-height: 1;
  }
  .bee-site-nav__icons svg.bee-icon {
      color: var(--h-text-color);
      line-height: 1;
      vertical-align: middle;
      transition: color 0.2s ease-in-out;
      width: 22px;
      height: 22px;
      fill: currentColor;
  }
  .bee-site-nav__icons.bee-use__kalles svg.bee-icon--account {
      width: 24px;
      height: 24px;
  }
  .bee-site-nav__icons.bee-use__line svg.bee-icon {
    width: 25px;
    height: 25px;
  }
  .bee-site-nav__icon>a:hover svg.bee-icon {
      color: var(--h-text-color-hover);
  }
  .bee-site-nav__icon a { 
    display: inline-block;
    line-height: 1;
  }
  a.bee-push-menu-btn {
    position: relative;
    top: -2px;
  }
  .bee-site-nav__cart >a,a.bee-push-menu-btn,.bee-col__textSocial,.bee-col__textSocial a {color: var(--h-text-color)}
  .bee-site-nav__cart >a:hover,.bee-col__textSocial a:hover {color: var(--h-text-color-hover)}
  .bee-search-header__main {
    opacity: 0.5;
    transition: opacity 0.3s;
  }
  .bee-search-header__main:hover {
      opacity: 1;
  }
  .bee-h-cart__design3 .bee-site-nav__heart .bee-count-box {
    margin-left: 3px;
  }
{%- if se_stts.full_header -%}
  @media (min-width: 1200px) {
    .bee-top-bar >.bee-container, .bee-header__wrapper >.bee-container,.bee-announcement-bar > .bee-container {
      padding-top: var(--pd-top);
      padding-right: var(--pd-right);
      padding-bottom: var(--pd-bottom);
      padding-left: var(--pd-left);
    }
  }
  @media (min-width: 1025px) and (max-width:1199px) {
    .bee-top-bar >.bee-container, .bee-header__wrapper >.bee-container,.bee-announcement-bar > .bee-container {
      padding: 0 30px;
    } 
  }
  {%- endif -%}
  @media (min-width: 1280px) {
    .bee-site-nav__icons .bee-site-nav__icon {
        padding: 0 14px;
    }
  }
  @media (min-width: 1025px) {
      .bee-section-header [data-header-height] {
          min-height: {{se_stts.height}}px;    
      }
      {%- if se_stts.full_header -%}
      .bee-announcement-bar >.bee-container, .bee-top-bar >.bee-container, .bee-header__wrapper >.bee-container {
          max-width: 100%;
      }
      {%- else -%}
         {%- if space_transparent and h_transparent -%}
          .is--topbar-transparent.is--header-transparent #shopify-section-top-bar,
          html:not(.is-header--stuck) .bee-section-header {
            max-width: 1170px;
            margin-right: auto;
            margin-left: auto;
          }
        {%- endif -%}
        .bee-header__wrapper>.bee-container {
          padding-right: 20px;
          padding-left: 20px;
        }
      {%- endif -%}
    .bee-site-nav__btnMenu svg {
        transform: rotate(180deg);
        margin-top: 8px;
    }
    .bee-col__textSocial p { margin-bottom: 0; }
    .bee-layout-layout_logo_search .bee-site-nav__icon.bee-site-nav__search {
      display:none
    }
  }
{%- endstyle -%}

{%- if h_transparent -%}
<script>
document.documentElement.classList.add('is--header-transparent');
document.documentElement.style.setProperty('--header-height', document.getElementById('shopify-section-{{ section.id }}').offsetHeight + 'px');
</script>
{%- endif -%}

{%- schema -%}
  {
   "name": "Header sidebar",
   "tag": "header",
   "class": "bee-section bee-section-header",
   "settings": [
      /*{
        "type": "paragraph",
        "content": "Only working desktop"
      },*/
      {
        "type": "select",
        "id": "layout",
         "options": [
          {
            "value": "menu_left",
            "label": "Button open menu left"
          },
          {
            "value": "menu_right",
            "label": "Button open menu right"
          },
          {
            "value": "logo_search",
            "label": "Search form"
          }
         ],
        "label": "Header layout",
        "default": "menu_left"
      },
      {
        "type": "checkbox",
        "id": "full_header",
        "info": "Make header full width",
        "label": "Enable full Width",
        "default": true
      },
      /*{
        "type": "header",
        "content": "+ Options only working desktop"
      },*/
      {
        "type": "range",
        "id": "height",
        "label": "Custom header height",
        "min": 60,
        "max": 160,
        "step": 1,
        "unit": "px",
        "default": 62
      },
      {
        "type": "range",
        "id": "frm_sea_mw",
        "label": "Search form max width",
        "info": "Only working when search form active",
        "min": 300,
        "max": 1600,
        "step": 100,
        "unit": "px",
        "default": 600
      },
      {
        "type": "checkbox",
        "id": "search_border",
        "label": "Use Search Border",
        "default": true
      },
      {
        "type": "header",
        "content": "+ Options only working Tablet, mobile"
      },
      {
        "type": "range",
        "id": "h_navmb",
        "label": "Custom header mobile height",
        "min": 60,
        "max": 160,
        "step": 1,
        "unit": "px",
        "default": 62
      },
      {
        "type": "header",
        "content": "+ Header Colors:"
      },
      {
        "type": "image_picker",
        "id": "h__bgimg",
        "label": "Header Background image"
      },
      {
        "type": "color",
        "id": "bgnav",
        "label": "Header background color",
        "default": "#ffffff"
      },
      {
        "type": "range",
        "id": "opnav",
        "label": "Background opacity",
        "default": 100,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "color",
        "id": "clnav",
        "label": "Header text/icon color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "clnav_hover",
        "label": "Header text/icon color when hover",
        "default": "#544179"
      },
      
      {
        "type": "checkbox",
        "id": "show_border",
        "label": "Use border bottom",
        "default": false
      },
      {
        "type": "color",
        "id": "bd_cl",
        "label": "Border Color",
        "default": "#000"
      },
      {
        "type": "header",
        "content": "+ Header Group buttons:"
      },
      {
        "type": "select",
        "id": "h_icon",
        "options": [
          {
            "value": "nitro",
            "label": "Nitro icon"
          },
          {
            "value": "pe",
            "label": "Pe icon"
          },
          {
            "value": "drawn",
            "label": "Drawn icon"
          },
          {
            "value": "line",
            "label": "Line awesome"
          }
        ],
        "label": "Design icon:",
        "default": "nitro"
      },
      {
        "type": "checkbox",
        "id": "h_txt_lable",
        "label": "Enable replace icon with text on desktop",
        "info": "Warning: For 'HEADER LAYOUT MENU SPLIT' maybe over text when show more",
        "default": false
      },
      {
        "type": "select",
        "id": "hover_icon",
        "options": [
          {
            "value": "1",
            "label": "Simple"
          },
          {
            "value": "2",
            "label": "Zoom"
          },
          {
            "value": "3",
            "label": "Zoom and skew"
          }
        ],
        "label": "Hover effect icon:",
        "default": "2"
      },
      {
        "type": "checkbox",
        "id": "show_search",
        "label": "Show search icon?",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_wis",
        "label": "Show wishlist icon?",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_compare",
        "label": "Show compare icon?",
        "default": false
      },
      {
        "type": "select",
        "id": "account_des",
        "options": [
          {
            "value": "0",
            "label": "Disable"
          },
          {
            "value": "1",
            "label": "Account icon"
          },
          {
            "value": "2",
            "label": "Account text"
          }
        ],
        "label": "Account:",
        "default": "1",
        "info": "Set your account design in the header."
      },
      {
        "type": "select",
        "id": "cart_des",
        "options": [
          {
            "value": "0",
            "label": "Disable"
          },
          {
            "value": "1",
            "label": "Cart count"
          },
          {
            "value": "2",
            "label": "Cart count, total price"
          },
          {
            "value": "3",
            "label": "Cart count 2"
          },
          {
            "value": "4",
            "label": "Cart total price"
          },
          {
            "value": "5",
            "label": "Cart divider, total price"
          }
        ],
        "label": "Shopping cart:",
        "default": "1",
        "info": "Set your shopping cart widget design in the header."
      },
      {
        "type": "color",
        "id": "bg_hc",
        "label": "Count background color",
        "default": "#000000"
      },
      {
        "type": "color",
        "id": "cl_hc",
        "label": "Count text color",
        "default": "#ffffff"
      },
      {
        "type": "header",
        "content": "+ Sticky header"
      }, 
      {
        "type": "checkbox",
        "id": "sticky_header",
        "label": "Enable sticky header",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "scroll_header",
        "label": "Hide sticky header on scroll down",
        "default": true
      },
      {
        "type": "color",
        "id": "bgnavst",
        "label": "Header background color",
        "default": "#ffffff"
      },
      {
        "type": "range",
        "id": "opnavst",
        "label": "Background opacity",
        "default": 100,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "color",
        "id": "clnavst",
        "label": "Header text/icon color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "clnavst_hover",
        "label": "Header text/icon color when hover",
        "default": "#544179"
      },

      {
        "type": "header",
        "content": "+ Transparent header"
      },
      {
        "type": "checkbox",
        "id": "transparent_header",
        "label": "Enable transparent header",
        "info": "Only active on homepage",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "transparent_collection",
        "label": "Enable transparent header on collection page",
        "info": "Only active on collection",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "transparent_blog",
        "label": "Enable transparent header on blog page",
        "info": "Only active on article",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "transparent_article",
        "label": "Enable transparent header on article page",
        "info": "Only active on article",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "space_transparent_header",
        "label": "Enable transparent header space top",
        "info": "Only active on homepage and transparent header active",
        "default": false
      },
      {
        "type": "color",
        "id": "bgnavtr",
        "label": "Header background color",
        "default": "#000000"
      },
      {
        "type": "range",
        "id": "opnavtr",
        "label": "Background opacity",
        "default": 40,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "color",
        "id": "clnavtr",
        "label": "Header text/icon color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "clnavtr_hover",
        "label": "Header text/icon color when hover",
        "default": "#ffffff"
      },
      
      {
        "type": "checkbox",
        "id": "show_border_tr",
        "label": "Use border bottom",
        "default": false
      },
      {
        "type": "color",
        "id": "bd_cl_tr",
        "label": "Border Color",
        "default": "#fff"
      },
      {
        "type": "range",
        "id": "bd_cl_op",
        "label": "Border Color opacity",
        "default": 15,
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%"
      },
      {
        "type": "header",
        "content": "+ Design options desktop",
        "info": "Only work on desktop when header full width"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
          "placeholder": ",15px,,15px",
          "default": ",15px,,15px"
      },
      {
        "type": "header",
        "content": "+ Layout header button opend menu right config"
      },
      {
        "type": "select",
        "id": "source",
        "options": [
          {
            "value": "0",
            "label": "None"
          },
          {
            "value": "1",
            "label": "Text"
          },
          {
            "value": "2",
            "label": "Social share"
          },
          {
            "value": "3",
            "label": "Social follow"
          }
        ],
        "label": "Left show:",
        "default": "2"
      },
      {
        "type": "richtext",
        "id": "txt",
        "label": "Text",
        "info": "You can place here some advertisement or phone numbers.",
        "default": "<p>Welcome to our store!</p>"
      }
   ]
}
{% endschema %}