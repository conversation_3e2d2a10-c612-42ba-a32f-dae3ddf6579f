<!-- sections/tabs-collection.liquid -->
{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 'bee-se-container'
    assign html_layout = '<div class="bee-container">__</div></div>' | split: '__'
  elsif stt_layout == 'bee-container-wrap'
    assign html_layout = '<div class="bee-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif

  assign search_url = routes.all_products_collection_url
  if bk_stts.btn_owl == "simple"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif
  assign show_btn = bk_stts.show_btn
  if show_btn != "hidden"
    assign slider_btns = true
  else
    assign slider_btns = false
  endif
  assign show_dots = bk_stts.show_dots
  if show_dots != "hidden"
    assign slider_dots = true
  else
    assign slider_dots = false
  endif

  assign title_inline = se_stts.title_inline
  assign tabs_des = se_stts.tabs_des
  assign tabs_pos = se_stts.tabs_pos
  if tabs_pos == "start"
    assign slider_cellAlign = "left"
  elsif tabs_pos == "end"
    assign slider_cellAlign = "right"
  else
    assign slider_cellAlign = tabs_pos
  endif
  assign use_link_vendor = settings.use_link_vendor
-%} 
{{ 'bee-section.css' | asset_url | stylesheet_tag }}
{{ 'bee-top-head.css' | asset_url | stylesheet_tag }}
{{ 'bee-tabs-collection.css' | asset_url | stylesheet_tag }}
{{ 'bee-collection-products.css' | asset_url | stylesheet_tag }}
{{ 'bee-slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'bee-base_drop.min.css' | asset_url }}" media="all">
<link href="{{ 'bee-loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">

{% assign current_tags = search.terms | split: ' ' %}
{%- if current_tags contains 'ntbeetag' and current_tags -%}
  {%- assign num_i = current_tags[0] |remove:'_bid' | plus:0 -%}
  {%- assign block = section.blocks[num_i] -%}
  {%- assign collection = collections[block.settings.collection] -%}
  {%- render 'inc_tab', ck_q:false, section:section, sid:sid, collection:collection, block:block, se_stts:se_stts, arrow_icon:arrow_icon -%}
{%- else -%}
<div class="bee-section-inner bee_nt_se_{{sid}} {{stt_layout}}{% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee{% endif %}" {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %} {% render 'section_style',se_stts:se_stts -%} >
    {{-html_layout[0]-}}
    {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner{% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2"{% endif %}>{% endif -%}
    <div class=" bee-tabs-se tabs-layout-{{tabs_des}} {% if title_inline %}bee-tabs-inline{% endif %} bee-tabs-{{tabs_des}} bee-item-rounded-{{se_stts.item_rounded}} bee-border-{{se_stts.tabs_border}}" style="--border:{{ se_stts.tabs_border }};--pri-cl:{{ se_stts.pri_cl }};--secon-cl:{{ se_stts.secon_cl }};--pri-active-cl:{{ se_stts.pri_active_cl }};--secon-active-cl:{{ se_stts.secon_active_cl }};--item-fs:{{ se_stts.item_fs }}px;--item-fw:{{ se_stts.item_fw }};--item-lh:{{ se_stts.item_lh }}px;--item-ls:{{ se_stts.item_ls }}px;--icon-width:{{ se_stts.icon_width }}px;--space-between:{{ se_stts.space_between }}px;--space-between-tb:{{ se_stts.space_between_tb }}px;--space-between-mb:{{ se_stts.space_between_mb }}px;--tablist-mgb:{{ se_stts.tabslist_mb }}px;">
      {%- if title_inline == false -%}
        {%- render 'section_tophead',se_stts:se_stts -%}
      {%- endif -%}
      <div class="bee-tabs bee-type-tabs bee-text-{{tabs_pos}}" data-bee-tabs2>
        {%- if title_inline -%}
          <div class="bee-tabs-head">
            {%- render 'section_tophead',se_stts:se_stts -%}
        {%- endif -%}
        <ul data-bee-tab-ul2 class="bee-tabs-ul bee-align-items-center bee-justify-content-{{tabs_pos}} bee-flickity-slider bee-slider-btn__in-content bee-slider-btn-show_all bee-slider-btn-style-simple bee-slider-btn-none bee-slider-btn-small bee-slider-btn-cl-{{ bk_stts.btn_cl }} bee-slider-btn-vi-always flickitybee" 
           data-flickitybee-js='{"isSimple": true , "freeScroll": true, "arrowIcon":"1", "imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign":"{{slider_cellAlign}}", "wrapAround": false,"prevNextButtons": true,"percentPosition": 0,"pageDots": false, "pauseAutoPlayOnHover" : true }'>  
          {%- for block in se_blocks -%}
            {%- liquid
              assign bk_stts    = block.settings
              assign blockid    = block.id
              assign title      = bk_stts.title
              assign image      = bk_stts.image_title
              assign icon_title = bk_stts.icon_title
            -%}

            <li class="bee-tab-item bee-d-inline-flex"><a id="b_{{block.id}}" href="#bee-{{blockid}}" rel="nofollow" data-bee-tab-item data-no-instant {{ block.shopify_attributes }} {%if forloop.first == true %} class="bee-active" {% endif %}>
              <span>
                {%- if icon_title != blank -%}  
                  <span class="bee-icon-title"><i class="{{ icon_title }}"></i></span>
                {%- elsif bk_stts.image_title != blank -%}
                  <span class="bee-image-title">
                    <img class="lazyloadbee bee-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg',w: image.width,h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                    <span class="lazyloadbee-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }});"></span>
                  </span>
                {%- endif -%}
                <span class="bee-text-title">{{bk_stts.title}}</span>
              </span>
            </a></li>
          {%- endfor -%}
        </ul>
        {%- if title_inline -%}
          </div>
        {%- endif -%}
        <div class="bee-pr bee-tab-contents2 bee-oh">
          {%- if request.design_mode -%}
            {%- for block in se_blocks -%}
              {%- assign collection = collections[block.settings.collection]  -%}
              <div id="bee-{{block.id}}" class="bee-tab-content2 {% if forloop.first == true %} bee-active {% endif %}" data-bee-tab-content data-render-lazy-component >
                {%- render 'inc_tab', ck_q:true, section:section, sid:sid, collection:collection, block:block, se_stts:se_stts, arrow_icon:arrow_icon, use_link_vendor:use_link_vendor -%}
              </div>
            {%- endfor -%}
          {%- else -%}
              {%- assign block = section.blocks[0] -%}
              {%- assign collection = collections[block.settings.collection]  -%}
              <div id="bee-{{block.id}}" class="bee-tab-content2 bee-active" data-bee-tab-content data-render-lazy-component >
                {%- render 'inc_tab', ck_q:true, section:section, sid:sid, collection:collection, block:block, se_stts:se_stts, arrow_icon:arrow_icon, use_link_vendor:use_link_vendor -%}
              </div>
              {%- for block in section.blocks offset:1 -%}
              {%- assign collection = collections[block.settings.collection] -%}
                <div id="bee-{{block.id}}" class="bee-tab-content2 lazyloadbee" data-bee-tab-content data-render-lazy-component data-renderbee="{{ routes.search_url }}?type=article&q={{ forloop.index0 }}_bid+ntbeetag" data-sebeeurl='/?section_id={{sid}}' data-beeplitlz><div class="lds_bginfinity bee-pr"></div></div>
              {%- endfor -%}
          {%- endif -%}
        </div>
      </div>
    </div>
    {{-html_layout[1]-}}
</div>
{%- endif -%}
{% schema %}
  {
    "name": "Tabs Collection",
    "tag": "section",
    "class": "bee-section bee-section-all bee_bk_flickity bee_tp_cd bee-tabs-collection bee_tp_tab2",
    "settings": [
      {
            "type": "header",
            "content": "1. Heading options"
        },
        // {
        //     "type": "select",
        //     "id": "design_heading",
        //     "label": "Heading design",
        //     "default": "1",
        //     "options": [
        //         {
        //             "value": "1",
        //             "label": "Design 01"
        //         },
        //         {
        //             "value": "2",
        //             "label": "Design 02"
        //         },
        //         {
        //             "value": "3",
        //             "label": "Design 03"
        //         },
        //         {
        //             "value": "4",
        //             "label": "Design 04"
        //         },
        //         {
        //             "value": "5",
        //             "label": "Design 05"
        //         }
        //     ]
        // },
        {
            "type": "checkbox",
            "id": "heading_center",
            "label": "Center heading align",
            "default": true
        },
        {
            "type": "text",
            "id": "top_heading",
            "label": "Heading",
            "default": "Trending Products"
        },
        {
            "type": "richtext",
            "id": "top_subheading",
            "label": "Description"
        },
        {
            "type": "number",
            "id": "tophead_mb",
            "label": "Bottom space (Desktop)(px)",
            "info": "The spacing is between the heading and the content",
            "default": 30
        },
      {
          "type": "number",
          "id": "tophead_mb_tb",
          "label": "Bottom space (Tablet)(px)",
          "info": "The spacing is between the heading and the content",
          "default": 30
      },
      {
          "type": "number",
          "id": "tophead_mb_mb",
          "label": "Bottom space (Mobile)(px)",
          "info": "The spacing is between the heading and the content",
          "default": 25
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type": "checkbox",
        "id": "title_inline",
        "label": "Title inline",
        "info": "Tick here to show Tab title and tab list on 1 line",
        "default": false
      },
      {
        "type": "select",
        "id": "tabs_des",
        "options": [
          {
            "value": "base",
            "label": "Base"
          },
          {
            "value": "bg-active",
            "label": "Has background (when item active)"
          },
          {
            "value": "underline",
            "label": "Has underline (when item active)"
          }
        ],
        "label": "Tabs design",
        "default": "base"
      },
      {
        "type": "select",
        "id": "tabs_pos",
        "label": "Tabs List Position",
        "default": "center",
        "options": [
          {
            "value": "start",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "end",
            "label": "Right"
          }
        ]
      },
      {
        "type": "number",
        "id": "tabslist_mb",
        "label": "Tabs list margin bottom",
        "default": 30
      },
      {
        "type": "number",
        "id": "tabslist_mb_tb",
        "label": "Tabs list margin bottom (Tablet)",
        "default": 30
      },
      {
        "type": "number",
        "id": "tabslist_mb_mb",
        "label": "Tabs list margin bottom (Mobile)",
        "default": 30
      },
      {
        "type": "header",
        "content": "+ Tab items options"
      },
      {
        "type": "range",
        "id": "item_fs",
        "label": "Font size",
        "max": 100,
        "min": 10,
        "step": 1,
        "unit": "px",
        "default": 16
      },
      {
        "type": "range",
        "id": "item_lh",
        "label": "Line height",
        "max": 100,
        "min": 0,
        "step": 1,
        "default": 30,
        "unit": "px",
        "info": "Set '0' to use default"            
      },
      {
        "type": "range",
        "id": "item_fw",
        "label": "Font weight",
        "min": 100,
        "max": 900,
        "step": 100,
        "default": 500
      },
      {
        "type": "range",
        "id": "item_ls",
        "label": "Letter spacing",
        "max": 5,
        "min": -5,
        "default": 0,
        "step": 0.1,
        "unit": "px"
      },
      {
        "type": "range",
        "id": "icon_width",
        "label": "Image / Icon size (If available)",
        "max": 100,
        "min": 20,
        "step": 1,
        "unit": "px",
        "default": 30
      },
      {
        "type": "checkbox",
        "id": "item_rounded",
        "label": "Tabs item rounded",
        "info": "Only working with design has border and background",
        "default": true
      },
      {
        "type": "color",
        "id": "pri_cl",
        "label": "Primary color",
        "default": "#151515"
      },
      {
        "type": "color",
        "id": "secon_cl",
        "label": "Secondary color",
        "default": "#858585"
      },
      {
        "type": "color",
        "id": "pri_active_cl",
        "label": "Primary active color",
        "default": "#222222"
      },
      {
        "type": "color",
        "id": "secon_active_cl",
        "default": "#ffffff",
        "label": "Secondary active color"      
      },
      {
        "type": "range",
        "id": "space_between",
        "min": 0,
        "max": 60,
        "step": 1,
        "label": "Space between items (Desktop)",
        "unit": "px",
        "default": 30
      },
      {
        "type": "range",
        "id": "space_between_tb",
        "min": 0,
        "max": 60,
        "step": 1,
        "label": "Space between items (Tablet)",
        "unit": "px",
        "default": 20
      },
      {
        "type": "range",
        "id": "space_between_mb",
        "min": 0,
        "max": 60,
        "step": 1,
        "label": "Space between items (Mobile)",
        "unit": "px",
        "default": 15
      },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "bee-container-wrap","label": "Layout",
        "options": [
            { "value": "bee-se-container", "label": "Container"},
            { "value": "bee-container-wrap", "label": "Wrapped container"},
            { "value": "bee-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info": "Margin top, margin right, margin bottom, margin left. If you do not use it please blank.",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design tablet options"
      },
      {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }
    ],
    "blocks": [
      {
        "type": "tab_item",
        "name": "Tab item",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Tab title",
            "default": "Tab title"
          },
          {
            "type": "text",
            "id": "icon_title",
            "label": "Enter a icon name on tab title",
            "info": "[LineAwesome](https://kalles.the4.co/font-lineawesome/). Leave it blank if you want to use an image"
          },
          {
            "type": "image_picker",
            "id": "image_title",
            "label": "Image on title item"
          },
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection"
          },
          {
            "type": "select",
            "id": "product_des",
            "options": [
              {
                "value": "1",
                "label": "Design 1"
              },
              {
                "value": "2",
                "label": "Design 2"
              },
              {
                "value": "3",
                "label": "Design 3"
              },
              {
                "value": "4",
                "label": "Design 4"
              },
              {
                "value": "5",
                "label": "Design 5"
              },
              {
                "value": "6",
                "label": "Design 6"
              },
              {
                "value": "7",
                "label": "Design 7"
              }
            ],
            "label": "Product item design",
            "info":  "Featured item if you chose",
            "default": "1"
          },
          {
            "type": "checkbox",
            "id": "show_vendor",
            "label": "Show product vendors",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "use_cdt",
            "label": "Show product countdown",
            "default": false
          },
          {
            "type": "header",
            "content": "+ Options image products"
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "rationt",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratiocus3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "select",
            "id": "content_align",
            "label": "Product content align",
            "default": "start",
            "options": [
              {
                "label": "Default",
                "value": "start"
              },
              {
                "label": "Center",
                "value": "center"
              }
            ]
          },
          {
            "type": "range",
            "id": "limit",
            "min": 1,
            "max": 50,
            "step": 1,
            "label": "Maximum products to show",
            "default": 8
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              },
              {
                "value": "5",
                "label": "5"
              },
              {
                "value": "6",
                "label": "6"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_tb",
            "label": "Items per row (Tablet)",
            "default": "3",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              }
            ]
          },
          {
            "type": "select",
            "id": "col_mb",
            "label": "Items per row (Mobile)",
            "default": "2",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              }
            ]
          },
          {
            "type": "select",
            "id": "space_h_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              },
              {
                  "value": "40",
                  "label": "40px"
              },
              {
                  "value": "50",
                  "label": "50px"
              }
            ],
            "label": "Space vertical items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_h_item_tb",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items (Tablet)",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_v_item_tb",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              },
              {
                  "value": "40",
                  "label": "40px"
              }
            ],
            "label": "Space vertical items (Tablet)",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_h_item_mb",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items (Mobile)",
            "default": "10"
          },
          {
            "type": "select",
            "id": "space_v_item_mb",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items (Mobile)",
            "default": "10"
          },
          {
            "type": "header",
            "content": "--Box options--"
          },
          {
            "type": "select",
            "id": "layout_des",
            "options": [
              {
                "value": "1",
                "label": "Grid"
              },
              {
                "value": "2",
                "label": "Carousel"
              },
              {
                "value": "3",
                "label": "Masonry"
              }
            ],
            "label": "Layout design",
            "default": "2"
          },
          {
            "type": "header",
            "content": "+Options for carousel layout"
          },
          {
              "type": "checkbox",
              "id": "loop",
              "label": "Enable loop",
              "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
              "default": true
          },
          {
              "type": "range",
              "id": "au_time",
              "min": 0,
              "max": 30,
              "step": 0.5,
              "label": "Autoplay speed in second.",
              "info": "Set is '0' to disable autoplay",
              "unit": "s",
              "default": 0
          },
          {
              "type": "checkbox",
              "id": "au_hover",
              "label": "Pause autoplay on hover",
              "info": "Auto-playing will pause when the user hovers over the carousel",
              "default": true
          },
          {
              "type": "paragraph",
              "content": "—————————————————"
          },
          {
              "type": "paragraph",
              "content": "Prev next button"
          },
          {
            "type": "select",
            "id": "show_btn",
             "options": [
              {
                "value": "show_all",
                "label": "Show all screen"
              },
              {
                "value": "show_desktop",
                "label": "Only show on desktop"
              },
              {
                "value": "show_mobile",
                "label": "Only show on tablet & mobile"
              },
              {
                "value": "hidden",
                "label": "Hidden"
              }
            ],
            "label": "Use prev next button",
            "default": "show_all"
          },
          {
              "type": "select",
              "id": "icon_slider",
              "label": "Prev next icon",
              "default": "1",
              "options": [
                  {
                      "label": "Default",
                      "value": "0"
                  },
                  {
                      "label": "Solid",
                      "value": "1"
                  }
              ]
          },
          {
              "type": "select",
              "id": "btn_pos",
              "label": "Prev next position",
              "info": "Working on screen Desktop",
              "default": "between",
              "options": [
                  {
                      "label": "Default",
                      "value": "between"
                  },
                  {
                      "label": "In content",
                      "value": "in"
                  }
              ]
          },
          {
              "type": "range",
              "id": "btn_distance",
              "min": 0,
              "max": 100,
              "step": 1,
              "label": "Distance from buttons to boundary",
              "info": "Only works when \"Prev next position is In content\". Only works on desktop.",
              "unit": "px",
              "default": 15
          },
          {
              "type": "select",
              "id": "btn_vi",
              "label": "Visible",
              "default": "hover",
              "options": [
                  {
                      "value": "always",
                      "label": "Always"
                  },
                  {
                      "value": "hover",
                      "label": "Only hover"
                  }
              ]
          },
          {
              "type": "select",
              "id": "btn_owl",
              "label": "Button style",
              "default": "default",
              "options": [
                  {
                      "value": "default",
                      "label": "Default"
                  },
                  {
                      "value": "outline",
                      "label": "Outline"
                  },
                  {
                      "value": "simple",
                      "label": "Simple"
                  }
              ]
          },
          {
              "type": "select",
              "id": "btn_shape",
              "label": "Button shape",
              "info": "Not work for 'Simple' button style",
              "default": "none",
              "options": [
                  {
                      "value": "none",
                      "label": "Default"
                  },
                  {
                      "value": "round",
                      "label": "Round"
                  },
                  {
                      "value": "rotate",
                      "label": "Rotate"
                  }
              ]
          },
          {
              "type": "select",
              "id": "btn_cl",
              "label": "Button color",
              "default": "dark",
              "options": [
                  {
                      "value": "light",
                      "label": "Light"
                  },
                  {
                      "value": "dark",
                      "label": "Dark"
                  },
                  {
                      "value": "primary",
                      "label": "Primary"
                  },
                  {
                      "value": "custom1",
                      "label": "Custom color 1"
                  },
                  {
                      "value": "custom2",
                      "label": "Custom color 2"
                  },
                  {
                      "value": "custom3",
                      "label": "Custom color 3"
                  }
              ]
          },
          {
              "type": "select",
              "id": "btn_size",
              "label": "Buttons size",
              "default": "small",
              "options": [
                  {
                      "value": "small",
                      "label": "Small"
                  },
                  {
                      "value": "medium",
                      "label": "Medium"
                  },
                  {
                      "value": "large",
                      "label": "Large"
                  }
              ]
          },
          {
              "type": "paragraph",
              "content": "—————————————————"
          },
          {
              "type": "paragraph",
              "content": "Page dots"
          },
          {
            "type": "select",
            "id": "show_dots",
            "info": "Creates and show page dots",
             "options": [
              {
                "value": "show_all",
                "label": "Show all screen"
              },
              {
                "value": "show_desktop",
                "label": "Only show on desktop"
              },
              {
                "value": "show_mobile",
                "label": "Only show on tablet & mobile"
              },
              {
                "value": "hidden",
                "label": "Hidden"
              }
            ],
            "label": "Use carousel's dots",
            "default": "hidden"
          },
          {
              "type": "select",
              "id": "dot_owl",
              "label": "Dots style",
              "default": "default",
              "options": [
                  {
                      "value": "default",
                      "label": "Default"
                  },
                  {
                      "value": "background-active",
                      "label": "Background Active"
                  },
                  {
                      "value": "dots_simple",
                      "label": "Dots simple"
                  },
                  {
                      "value": "elessi",
                      "label": "Elessi"
                  },
                  {
                      "value": "br-outline",
                      "label": "Outline"
                  },
                  {
                      "value": "outline-active",
                      "label": "Outline active"
                  }
              ]
          },
          {
              "type": "select",
              "id": "dots_cl",
              "label": "Dots color",
              "default": "dark",
              "options": [
                  {
                      "value": "light",
                      "label": "Light (Best on dark background)"
                  },
                  {
                      "value": "dark",
                      "label": "Dark"
                  },
                  {
                      "value": "primary",
                      "label": "Primary"
                  },
                  {
                      "value": "custom1",
                      "label": "Custom color 1"
                  },
                  {
                      "value": "custom2",
                      "label": "Custom color 2"
                  },
                  {
                      "value": "custom3",
                      "label": "Custom color 3"
                  }
              ]
          },
          {
              "type": "checkbox",
              "id": "dots_round",
              "label": "Enable round dots",
              "default": true
          },
          {
              "type": "range",
              "id": "dots_space",
              "min": 2,
              "max": 20,
              "step": 1,
              "label": "Space among dots",
              "unit": "px",
              "default": 10
          },
          {
              "type": "range",
              "id": "dots_bottom_pos",
              "min": 0,
              "max": 100,
              "step": 1,
              "label": "Distance from dots to boundary",
              "unit": "px",
              "default": 20
          },
          {
            "type": "header",
            "content": "+Options for grid or masonry layout"
          },
          {
            "type": "checkbox",
            "id": "featured_item",
            "label": "Show featured item",
            "info": "Only work with layout 'Masonry'. Regardless of option 'Items per row'. ",
            "default": false
          },
          {
            "type": "select",
            "id": "use_pagination",
            "label": "Pagination",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "load-more",
                "label": "'Load more' button"
              },
              {
                "value": "view-all",
                "label": "'View all' button"
              }
            ]
          },
          {
            "type": "text",
            "id": "btn_replace",
            "label": "Title of the replace button",
            "info": "Leave empty to use 'View all' or 'Load more' default."
          },

          {
            "type": "select",
            "id": "btns_size",
            "label": "Button size",
            "default": "default",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Custom size #1",
                    "value": "1"
                },
                {
                    "label": "Custom size #2",
                    "value": "2"
                },
                {
                    "label": "Custom size #3",
                    "value": "3"
                }
            ]
          },
          {
            "type": "checkbox",
            "id": "btn_icon",
            "label": "Enable button icon",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "enable_bar_lm",
            "label": "Enable progress bar",
            "info": "Only active when you use 'Load more'",
            "default": true
          },
          {
            "type": "select",
            "id": "style_bar_lm",
            "label": "Style progress bar",
            "default": "default",
            "options": [
              {
                  "value": "default",
                  "label": "Default"
              },
              {
                  "value": "button",
                  "label": "Button"
              },
              {
                  "value": "cricle",
                  "label": "Cricle"
              }
            ]
          },
          {
            "type": "color",
            "id": "progress_bar_primary_cl",
            "label": "Progress bar primary color",
            "default": "#E6E5ED"
          },
          {
            "type": "color",
            "id": "progress_bar_second_cl",
            "label": "Progress bar secondary color",
            "default": "#BFBEC8"
          },
          {
            "type": "color",
            "id": "progress_bar_text_cl",
            "label": "Progress bar text color",
            "default": "#27262C"
          },
          {
            "type": "color",
            "id": "progress_bar_active_cl",
            "label": "Progress bar hover color",
            "default": "#4C4B51"
          },

          {
            "type": "range",
            "id": "pagination_distance",
            "min": 0,
            "max": 100,
            "step": 1,
            "label": "Distance from pagination to boundary",
            "unit": "px",
            "default": 20
          },
          {
            "type": "select",
            "id": "pagination_position",
            "label": "Pagination position",
            "default": "bee-text-center",
            "options": [
              {
                "value": "bee-text-start",
                "label": "Left"
              },
              {
                "value": "bee-text-center",
                "label": "Center"
              },
              {
                "value": "bee-text-end",
                "label": "Right"
              }
            ]
          },

          {
            "type": "paragraph",
            "content": "+ NOTE: The button options below are not available when using a button or circular progress bar."
          },
          {
            "type": "select",
            "id": "button_style",
            "label": "Button style",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Outline",
                    "value": "outline"
                },
                {
                    "label": "Border bottom",
                    "value": "bordered"
                },
                {
                    "label": "Link",
                    "value": "link"
                }
            ]
          },
          {
            "type": "select",
            "id": "btns_cl",
            "label": "Button color",
            "default": "dark",
            "options": [
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              },
              {
                  "value": "custom3",
                  "label": "Custom color 3"
              }
            ]
          },
          {
              "type": "select",
              "id": "button_effect",
              "label": "Hover button effect",
              "default": "default",
              "info": "Only working button style default, outline",
              "options": [
                  {
                      "label": "Default",
                      "value": "default"
                  },
                  {
                      "label": "Fade",
                      "value": "fade"
                  },
                  {
                      "label": "Rectangle out",
                      "value": "rectangle-out"
                  },
                  {
                      "label": "Sweep to right",
                      "value": "sweep-to-right"
                  },
                  {
                      "label": "Sweep to left",
                      "value": "sweep-to-left"
                  },
                  {
                      "label": "Sweep to bottom",
                      "value": "sweep-to-bottom"
                  },
                  {
                      "label": "Sweep to top",
                      "value": "sweep-to-top"
                  },
                  {
                      "label": "Shutter out horizontal",
                      "value": "shutter-out-horizontal"
                  },
                  {
                      "label": "Outline",
                      "value": "outline"
                  },
                  {
                      "label": "Shadow",
                      "value": "shadow"
                  }
              ]
          }
        ]
      }
    ],
  "presets": [
      {
        "name": "Tabs Collection",
        "category": "group2",
        "blocks": [
          { "type": "tab_item",
            "settings": {
              "title": "Tab 01"
            }
          },
          { "type": "tab_item",
            "settings": {
              "title": "Tab 02"
            }
          },
          { "type": "tab_item",
            "settings": {
              "title": "Tab 03"
            }
          }
        ]
      }
    ]
  }
{% endschema %}