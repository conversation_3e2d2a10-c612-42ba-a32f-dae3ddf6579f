<!-- sections/collections-list.liquid -->
{{ 'bee-section.css' | asset_url | stylesheet_tag }}
{{ 'bee-collection-item.css' | asset_url | stylesheet_tag }}
{{ 'bee-slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
<link href="{{ 'bee-custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- liquid
  assign image_fix = image_nt | image_tag
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if se_stts.center_slide
  echo 'bee-carousel-center.css' | asset_url | stylesheet_tag
  endif
  if stt_layout == 'bee-se-container'
    assign html_layout = '<div class="bee-container">__</div></div>' | split: '__'
  elsif stt_layout == 'stretch'
    assign html_layout = '<div class="bee-container is--bee-stretch">__</div></div>' | split: '__'
    assign stt_layout  = 'bee-se-container'
  elsif stt_layout == 'bee-container-wrap'
    assign html_layout = '<div class="bee-container">__</div>' | split: '__'
  else
    assign html_layout = '__' | split: '__'
  endif
  assign heading_inline = se_stts.heading_inline

  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else
    assign imgatt = 'data-'
  endif
  assign b_effect = se_stts.b_effect
  assign img_effect = se_stts.img_effect
  assign open_link = se_stts.open_link
  assign content_align = se_stts.content_align
  if se_stts.btn_owl == "simple"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif
  assign show_btn = se_stts.show_btn
  if show_btn != "hidden"
    assign slider_btns = true
  else
    assign slider_btns = false
  endif
  assign show_dots = se_stts.show_dots
  if show_dots != "hidden"
    assign slider_dots = true
  else
    assign slider_dots = false
  endif

  assign collection_des           = se_stts.collection_des
  assign pri_cl_lightness         = se_stts.pri_cl | color_extract: 'lightness'
  assign pri_hover_cl_lightness   = se_stts.pri_hover_cl | color_extract: 'lightness'
  assign second_cl_lightness         = se_stts.second_cl | color_extract: 'lightness'
  assign second_hover_cl_lightness   = se_stts.second_hover_cl | color_extract: 'lightness'
  if pri_cl_lightness < 85
    assign pri_cl2 = "#ffffff"
  else
    assign pri_cl2 = "#151515"
  endif
  if pri_hover_cl_lightness < 85
    assign pri_hover_cl2 = "#ffffff"
  else
    assign pri_hover_cl2 = "#151515"
  endif
  if second_cl_lightness < 85
    assign second_cl2 = "#ffffff"
  else
    assign second_cl2 = "#151515"
  endif
  if second_hover_cl_lightness < 85
    assign second_hover_cl2 = "#ffffff"
  else
    assign second_hover_cl2 = "#151515"
  endif


 -%}
{%- capture append_style -%}
  {% if stt_image_bg.presentation.focal_point != '50.0% 50.0%' %}--background-position: {{ stt_image_bg.presentation.focal_point }}{% endif %}
{%- endcapture -%}
<div class="bee-section-inner bee-section-inline-{{ heading_inline }} bee_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee{% endif %}" {%- if stt_image_bg != blank and stt_layout != 'bee-se-container' -%} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5"{%- endif -%} {% render 'section_style', se_stts: se_stts, append_style: append_style %}>

    {{- html_layout[0] -}}
      {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner{% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee{% endif %}" {%- if stt_image_bg != blank -%} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5"{%- endif -%}>{%- endif -%}
      {%- if heading_inline -%}
        <div class="bee-section-inline-inner bee-heading-width-{{ se_stts.heading_width }}">
      {%- endif -%}
          {%- render 'section_tophead', se_stts: se_stts, get_height: true -%}
        {%- if se_stts.layout_des == "1" -%}
          <div class="bee-section-content bee-list-collections bee-collection-des-{{ collection_des }} bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }} bee-row  bee-justify-content-center bee-row-cols-lg-{{ se_stts.col_dk }} bee-row-cols-md-{{ se_stts.col_tb }} bee-row-cols-{{ se_stts.col_mb }} bee-gx-lg-{{ se_stts.space_h_item }} bee-gy-lg-{{ se_stts.space_v_item }} bee-gx-md-{{ se_stts.space_h_item_tb }} bee-gy-md-{{ se_stts.space_v_item_tb }} bee-gx-{{ se_stts.space_h_item_mb }} bee-gy-{{ se_stts.space_v_item_mb }}" style="--pri-cl: {{ se_stts.pri_cl }};--pri-cl2: {{ pri_cl2 }};--pri-hover-cl: {{ se_stts.pri_hover_cl }};--pri-hover-cl2: {{ pri_hover_cl2 }}; --second-cl: {{ se_stts.second_cl }};--second-cl2: {{ second_cl2 }};--second-hover-cl: {{ se_stts.second_hover_cl }}; --second-hover-cl2: {{ second_hover_cl2 }};--second-cl-rgb: {{ se_stts.second_cl | color_to_rgb | split:'(' | last | remove:')' }};--other-cl: {{ se_stts.other_cl }};--other-rgb: {{ se_stts.other_cl | color_to_rgb | split:'(' | last | remove:')' }};--item-rd:{{ se_stts.item_rd }}%;--title-fs: {{ se_stts.title_fs }}px;--title-lh: {{ se_stts.title_lh }}px;--title-fw: {{ se_stts.title_fw }};--content-fs: {{ se_stts.content_fs }}px;--button-fs: {{ se_stts.button_fs }}px;">
        {%- else -%}
          {%- liquid
          assign icon_slider = se_stts.icon_slider
          if icon_slider == "1"
            assign view_box = "0 0 22 22"
          endif -%}
          <div class="bee-section-content bee-list-collections bee-collection-des-{{ collection_des }} is--scrollbar_{{ se_stts.scrollbar }}{% if se_stts.center_slide %} is-carousel-center{% endif %}  bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }} bee-flickity-slider bee-gx-lg-{{ se_stts.space_h_item }} bee-gy-lg-{{ se_stts.space_v_item }} bee-gx-md-{{ se_stts.space_h_item_tb }} bee-gy-md-{{ se_stts.space_v_item_tb }} bee-gx-{{ se_stts.space_h_item_mb }} bee-gy-{{ se_stts.space_v_item_mb }} {% if slider_btns %} bee-slider-btn__{{ se_stts.btn_pos }}-content bee-slider-btn-{{show_btn}} bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }} {% endif %}{% if slider_dots %} bee-dots-style-{{ se_stts.dot_owl }} bee-slider-dots-{{show_dots}} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} {% endif %} bee-row bee-row-cols-lg-{{ se_stts.col_dk }} bee-row-cols-md-{{ se_stts.col_tb }} bee-row-cols-{{ se_stts.col_mb }}  flickitybee" data-flickitybee-js='{"customIcon":{{icon_slider}}, "viewBox":"{{view_box}}", "cellSelector": ".bee-collection-item","scrollbar": {{ se_stts.scrollbar }},"freeScroll": {{ se_stts.freeScroll }},"centerSlide": {{ se_stts.center_slide }},"beeid": "{{ sid }}","setPrevNextButtons": true, "arrowIcon": "{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ slider_btns }},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--pri-cl: {{ se_stts.pri_cl }};--pri-cl2: {{ pri_cl2 }};  --pri-hover-cl: {{ se_stts.pri_hover_cl }};--pri-hover-cl2: {{ pri_hover_cl2 }};--second-cl: {{ se_stts.second_cl }};--second-cl2: {{ second_cl2 }};--second-hover-cl: {{ se_stts.second_hover_cl }};--second-hover-cl2: {{ second_hover_cl2 }};--second-cl-rgb: {{ se_stts.second_cl | color_to_rgb | split:'(' | last | remove:')' }};--other-cl: {{ se_stts.other_cl }};--other-rgb: {{ se_stts.other_cl | color_to_rgb | split:'(' | last | remove:')' }};--other-cl: {{ se_stts.other_cl }};--other-cl: {{ se_stts.other_cl }};--other-rgb: {{ se_stts.other_cl | color_to_rgb | split:'(' | last | remove:')' }};--item-rd:{{ se_stts.item_rd }}%;--title-fs: {{ se_stts.title_fs }}px;--title-lh: {{ se_stts.title_lh }}px;--title-fw: {{ se_stts.title_fw }};--content-fs: {{ se_stts.content_fs }}px;--button-fs: {{ se_stts.button_fs }}px;--btn-distance: {{ se_stts.btn_distance }}px;--space-dots: {{ se_stts.dots_space }}px;--dots-bottom-pos: {{ se_stts.dots_bottom_pos }}px;">
        {%- endif -%}
        {%- if se_blocks.size > 0 -%}
            {%- for block in se_blocks -%}
              {%- assign bk_stts = block.settings -%}
              {%- capture current -%}{%- cycle 1, 2, 3, 4, 5, 6 -%}{%- endcapture -%}
              <div class="bee-col-item bee-collection-item bee-text-{{ content_align }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }}>
                {%- render 'collection_item', collection_des: collection_des, b_effect: b_effect, img_effect: img_effect, bk_stts: bk_stts, open_link: open_link, content_align: content_align, current: current -%}
              </div>
            {%- endfor -%}
            {%- if se_stts.scrollbar %}<div class="bee-carousel-scrollbar bee-carousel-scrollbar--{{ sid }} is--hidden">{{ 'bee-carousel-scrollbar.css' | asset_url | stylesheet_tag }}<div class="bee-carousel-scrollbar__bg"></div><div class="bee-carousel-scrollbar__drag"></div></div>{% endif -%}
        {%- endif -%}

        <!-- Steal Now Button -->
        <div class="steal-now-button-container" style="display: flex; justify-content: center; align-items: center; width: 100%; margin: 40px 0 20px;">
          {%- liquid
            assign button_size = se_stts.steal_button_size | default: 'medium'
            assign border_radius = se_stts.steal_button_border_radius | default: 4
            assign border_width = se_stts.steal_button_border_width | default: 1

            if button_size == 'small'
              assign padding = '8px 16px'
              assign font_size = '14px'
            elsif button_size == 'large'
              assign padding = '16px 32px'
              assign font_size = '18px'
            else
              assign padding = '12px 24px'
              assign font_size = '16px'
            endif
          -%}
          <style>
            @keyframes pulse {
              0% { transform: scale(1); }
              50% { transform: scale(1.05); }
              100% { transform: scale(1); }
            }

            @keyframes shake {
              0%, 100% { transform: translateX(0); }
              10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
              20%, 40%, 60%, 80% { transform: translateX(3px); }
            }

            .steal-now-button {
              transition: all 0.3s ease;
              background-color: {{ se_stts.steal_button_bg_color }};
              color: {{ se_stts.steal_button_text_color }};
              border: {{ border_width }}px solid {{ se_stts.steal_button_border_color }};
              border-radius: {{ border_radius }}px;
            }

            .steal-now-button:hover {
              {% assign animation = se_stts.steal_button_animation | default: 'lift' %}

              {% if animation == 'lift' %}
                transform: translateY(-3px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
              {% elsif animation == 'pulse' %}
                animation: pulse 1s infinite;
              {% elsif animation == 'shake' %}
                animation: shake 0.5s;
              {% endif %}

              background-color: {{ se_stts.steal_button_hover_bg_color }};
              color: {{ se_stts.steal_button_hover_text_color }};
              border-color: {{ se_stts.steal_button_hover_border_color }};
            }
          </style>
          <a href="{{ se_stts.steal_button_link }}" class="steal-now-button" style="padding: {{ padding }}; font-weight: bold; font-size: {{ font_size }}; text-decoration: none; display: inline-block; text-align: center; cursor: pointer;">
            {{ se_stts.steal_button_text | default: 'Steal Now' }}
          </a>
        </div>
      {%- if heading_inline -%}
        </div>
      {%- endif -%}



      {{- html_layout[1] -}}
  </div>
{% schema %}
  {
    "name": "Collections list",
    "tag": "section",
    "class": "bee-section bee_bk_flickity bee-section-all bee_tp_cdt bee-collections-list bee-oh",
    "settings": [
      {
          "type": "header",
          "content": "1. Heading options"
      },
      {
          "type": "text",
          "id": "top_heading",
          "label": "Heading",
          "default": "Trending collection"
      },
      {
          "type": "richtext",
          "id": "top_subheading",
          "label": "Subheading"
      },
      {
          "type": "checkbox",
          "id": "heading_center",
          "label": "Center heading align",
          "default": true
      },
      {
          "type": "number",
          "id": "tophead_mb",
          "label": "Bottom space (Desktop)(px)",
          "info": "The spacing is between the heading and the content",
          "default": 30
      },
      {
          "type": "number",
          "id": "tophead_mb_tb",
          "label": "Bottom space (Tablet)(px)",
          "info": "The spacing is between the heading and the content",
          "default": 30
      },
      {
          "type": "number",
          "id": "tophead_mb_mb",
          "label": "Bottom space (Mobile)(px)",
          "info": "The spacing is between the heading and the content",
          "default": 25
      },
      {
          "type": "checkbox",
          "id": "heading_inline",
          "label": "Heading inline",
          "info": "Heading and section content are in a line. Only works on desktop",
          "default": false
      },
      {
        "type": "select",
        "id": "heading_width",
        "label": "Heading content width",
        "info": "Only works when enable heading inline",
        "default": "small",
        "options": [
            {
              "label": "Small",
              "value": "small"
            },
            {
              "label": "Medium",
              "value": "medium"
            },
            {
              "label": "Large",
              "value": "large"
            }
        ]
      },
      {
        "type": "header",
        "content": "2. General options"
      },
      {
        "type": "select",
        "id": "collection_des",
        "label": "Collection item design",
        "default": "1",
        "options": [
          {
            "value": "1",
            "label": "Design 1"
          },
          {
            "value": "2",
            "label": "Design 2"
          },
          {
            "value": "3",
            "label": "Design 3"
          },
          {
            "value": "4",
            "label": "Design 4"
          },
          {
            "value": "5",
            "label": "Design 5"
          },
          {
            "value": "6",
            "label": "Design 6"
          },
          {
            "value": "7",
            "label": "Design 7"
          },
          {
            "value": "8",
            "label": "Design 8 (Only image)"
          }
        ]
      },
      {
          "type": "range",
          "id": "title_fs",
          "label": "Title font size",
          "max": 100,
          "min": 10,
          "step": 1,
          "unit": "px",
          "default": 14
      },
      {
          "type": "range",
          "id": "title_lh",
          "label": "Title line height",
          "max": 100,
          "min": 0,
          "step": 1,
          "default": 0,
          "unit": "px",
          "info": "Set '0' to use default"
      },
      {
          "type": "range",
          "id": "title_fw",
          "label": "Title font weight",
          "min": 100,
          "max": 900,
          "step": 100,
          "default": 500
      },
      {
          "type": "range",
          "id": "content_fs",
          "label": "Content font size",
          "max": 100,
          "min": 10,
          "step": 1,
          "unit": "px",
          "default": 14
      },
      {
          "type": "range",
          "id": "button_fs",
          "label": "Buttons font size",
          "max": 50,
          "min": 10,
          "step": 1,
          "unit": "px",
          "default": 14
      },
      {
        "type": "color",
        "id": "pri_cl",
        "label": "Primary content color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "second_cl",
        "label": "Secondary content color",
        "default": "#151515"
      },
      {
        "type": "color",
        "id": "pri_hover_cl",
        "label": "Primary content hover color",
        "default": "#151515"
      },
      {
        "type": "color",
        "id": "second_hover_cl",
        "label": "Secondary content hover color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "other_cl",
        "label": "Other color",
        "default": "#000000"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Content align",
        "default": "center",
        "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Center",
              "value": "center"
            }
        ]
      },
      {
        "type": "select",
        "id": "open_link",
        "info": "Works when the item has a link",
        "options": [
          {
            "value": "_self",
            "label": "Current window"
          },
         {
            "value": "_blank",
            "label": "New window"
          }
        ],
        "label": "Open link in",
        "default": "_self"
      },
      {
        "type": "header",
        "content": "+ Options image collection"
      },
      {
        "type": "range",
        "id": "item_rd",
        "min": 0,
        "max": 50,
        "step": 1,
        "label": "Image rounded",
        "unit": "%",
        "default": 0
      },
      {
        "type": "select",
        "id": "img_effect",
        "label": "Image hover effect",
        "info": "Waring: Hovering effect will resize your images. Only works on desktop",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "zoom",
            "label": "Zoom image"
          },
          {
            "value": "rotate",
            "label": "Rotate image "
          },
          {
            "value": "translateToTop",
            "label": "Move to top "
          },
          {
            "value": "translateToRight",
            "label": "Move to right"
          },
          {
            "value": "translateToBottom",
            "label": "Move to bottom"
          },
          {
            "value": "translateToLeft",
            "label": "Move to left"
          }
        ]
      },
      {
        "type": "select",
        "id": "b_effect",
        "label": "Collection hover effect",
         "info": "Only works on desktop",
        "default": "none",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "border-run",
            "label": "Border run"
          },
          {
            "value": "pervasive-circle",
            "label": "Pervasive circle"
          },
          {
            "value": "plus-zoom-overlay",
            "label": "Plus zoom overlay"
          },
          {
            "value": "dark-overlay",
            "label": "Dark overlay"
          },
          {
            "value": "light-overlay",
            "label": "Light overlay"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "ratio1_1",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratiocus3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "header",
        "content": "--Box options--"
      },
      {
        "type": "select",
        "id": "layout_des",
        "options": [
          {
            "value": "1",
            "label": "Grid"
          },
          {
            "value": "2",
            "label": "Carousel"
          }
        ],
        "label": "Layout design",
        "default": "2"
      },
      {
        "type": "select",
        "id": "col_dk",
        "label": "Items per row",
        "default": "4",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          },
          {
            "value": "6",
            "label": "6"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "3",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0",
              "label": "0"
          },
          {
              "value": "4",
              "label": "4px"
          },
          {
              "value": "5",
              "label": "5px"
          },
          {
              "value": "10",
              "label": "10px"
          },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
          {
              "value": "30",
              "label": "30px"
          },
          {
              "value": "40",
              "label": "40px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0",
              "label": "0"
          },
          {
              "value": "4",
              "label": "4px"
          },
          {
              "value": "5",
              "label": "5px"
          },
          {
              "value": "10",
              "label": "10px"
          },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
          {
              "value": "30",
              "label": "30px"
          },
          {
              "value": "40",
              "label": "40px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_tb",
        "options": [
          {
              "value": "0",
              "label": "0"
          },
          {
              "value": "4",
              "label": "4px"
          },
          {
              "value": "5",
              "label": "5px"
          },
          {
              "value": "10",
              "label": "10px"
          },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Tablet)",
        "default": "15"
      },
      {
        "type": "select",
        "id": "space_v_item_tb",
        "options": [
          {
              "value": "0",
              "label": "0"
          },
          {
              "value": "4",
              "label": "4px"
          },
          {
              "value": "5",
              "label": "5px"
          },
          {
              "value": "10",
              "label": "10px"
          },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Tablet)",
        "default": "15"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0",
              "label": "0"
          },
          {
              "value": "4",
              "label": "4px"
          },
          {
              "value": "5",
              "label": "5px"
          },
          {
              "value": "10",
              "label": "10px"
          },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "15"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0",
              "label": "0"
          },
          {
              "value": "4",
              "label": "4px"
          },
          {
              "value": "5",
              "label": "5px"
          },
          {
              "value": "10",
              "label": "10px"
          },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "15"
      },
      {
        "type": "header",
        "content": "+Options for carousel layout"
      },
      {
          "type": "checkbox",
          "id": "loop",
          "label": "Enable loop",
          "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
          "default": true
      },
      {
          "type": "range",
          "id": "au_time",
          "min": 0,
          "max": 30,
          "step": 0.5,
          "label": "Autoplay speed in second.",
          "info": "Set is '0' to disable autoplay",
          "unit": "s",
          "default": 0
      },
      {
          "type": "checkbox",
          "id": "au_hover",
          "label": "Pause autoplay on hover",
          "info": "Auto-playing will pause when the user hovers over the carousel",
          "default": true
      },
      {
          "type": "checkbox",
          "id": "freeScroll",
          "label": "Enable freeScroll",
          "info": "Enables content to be freely scrolled and flicked without aligning cells to an end position.",
          "default": false
      },
      {
          "type": "checkbox",
          "id": "scrollbar",
          "label": "Enable scrollbar",
          "default": false
      },
      {
          "type": "checkbox",
          "id": "center_slide",
          "label": "Enable center slide",
          "info": "Support maximun 5 columns. Only working when enable loop and should only be used when has the next slide.",
          "default": false
      },
      {
          "type": "paragraph",
          "content": "—————————————————"
      },
      {
          "type": "paragraph",
          "content": "Prev next button"
      },
      {
        "type": "select",
        "id": "show_btn",
         "options": [
          {
            "value": "show_all",
            "label": "Show all screen"
          },
          {
            "value": "show_desktop",
            "label": "Only show on desktop"
          },
          {
            "value": "show_mobile",
            "label": "Only show on tablet & mobile"
          },
          {
            "value": "hidden",
            "label": "Hidden"
          }
        ],
        "label": "Use prev next button",
        "default": "show_all"
      },
      {
          "type": "select",
          "id": "btn_pos",
          "label": "Prev next position",
          "info": "Working on screen Desktop",
          "default": "between",
          "options": [
              {
                  "label": "Default",
                  "value": "between"
              },
              {
                  "label": "In content",
                  "value": "in"
              },
              {
                  "label": "Out content",
                  "value": "out"
              },
              {
                  "label": "On top heading",
                  "value": "ontop"
              }
          ]
      },
        {
          "type": "select",
          "id": "icon_slider",
          "label": "Prev next icon",
          "default": "1",
          "options": [
              {
                  "label": "Default",
                  "value": "0"
              },
              {
                  "label": "Solid",
                  "value": "1"
              }
          ]
        },
      {
          "type": "range",
          "id": "btn_distance",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Distance from buttons to boundary",
          "info": "Only works when \"Prev next position is In content\". Only works on desktop.",
          "unit": "px",
          "default": 15
      },
      {
          "type": "select",
          "id": "btn_vi",
          "label": "Visible",
          "default": "hover",
          "options": [
              {
                  "value": "always",
                  "label": "Always"
              },
              {
                  "value": "hover",
                  "label": "Only hover"
              }
          ]
      },
      {
          "type": "select",
          "id": "btn_owl",
          "label": "Button style",
          "default": "default",
          "options": [
              {
                  "value": "default",
                  "label": "Default"
              },
              {
                  "value": "outline",
                  "label": "Outline"
              },
              {
                  "value": "simple",
                  "label": "Simple"
              }
          ]
      },
      {
          "type": "select",
          "id": "btn_shape",
          "label": "Button shape",
          "info": "Not work for 'Simple' button style",
          "default": "none",
          "options": [
              {
                  "value": "none",
                  "label": "Default"
              },
              {
                  "value": "round",
                  "label": "Round"
              },
              {
                  "value": "rotate",
                  "label": "Rotate"
              }
          ]
      },
      {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              },
                    {
                        "value": "custom3",
                        "label": "Custom color 3"
                    }
          ]
      },
      {
          "type": "select",
          "id": "btn_size",
          "label": "Buttons size",
          "default": "small",
          "options": [
              {
                  "value": "small",
                  "label": "Small"
              },
              {
                  "value": "medium",
                  "label": "Medium"
              },
              {
                  "value": "large",
                  "label": "Large"
              }
          ]
      },
      {
          "type": "paragraph",
          "content": "—————————————————"
      },
      {
          "type": "paragraph",
          "content": "Page dots"
      },
      {
        "type": "select",
        "id": "show_dots",
        "info": "Creates and show page dots",
         "options": [
          {
            "value": "show_all",
            "label": "Show all screen"
          },
          {
            "value": "show_desktop",
            "label": "Only show on desktop"
          },
          {
            "value": "show_mobile",
            "label": "Only show on tablet & mobile"
          },
          {
            "value": "hidden",
            "label": "Hidden"
          }
        ],
        "label": "Use carousel's dots",
        "default": "hidden"
      },
      {
          "type": "select",
          "id": "dot_owl",
          "label": "Dots style",
          "default": "default",
          "options": [
              {
                  "value": "default",
                  "label": "Default"
              },
              {
                  "value": "background-active",
                  "label": "Background Active"
              },
              {
                  "value": "dots_simple",
                  "label": "Dots simple"
              },
              {
                  "value": "elessi",
                  "label": "Elessi"
              },
              {
                  "value": "br-outline",
                  "label": "Outline"
              },
              {
                  "value": "outline-active",
                  "label": "Outline active"
              }
          ]
      },
      {
          "type": "select",
          "id": "dots_cl",
          "label": "Dots color",
          "default": "dark",
          "options": [
              {
                  "value": "light",
                  "label": "Light (Best on dark background)"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              },
                    {
                        "value": "custom3",
                        "label": "Custom color 3"
                    }
          ]
      },
      {
          "type": "checkbox",
          "id": "dots_round",
          "label": "Enable round dots",
          "default": true
      },
      {
          "type": "range",
          "id": "dots_space",
          "min": 2,
          "max": 20,
          "step": 1,
          "label": "Space among dots",
          "unit": "px",
          "default": 10
      },
      {
          "type": "range",
          "id": "dots_bottom_pos",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Distance from dots to boundary",
          "unit": "px",
          "default": 20
      },
      {
        "type": "header",
        "content": "3. Steal Now Button"
      },
      {
        "type": "text",
        "id": "steal_button_text",
        "label": "Button text",
        "default": "Steal Now"
      },
      {
        "type": "url",
        "id": "steal_button_link",
        "label": "Button link",
        "default": "/collections/all"
      },
      {
        "type": "color",
        "id": "steal_button_bg_color",
        "label": "Button background color",
        "default": "#ff4747"
      },
      {
        "type": "color",
        "id": "steal_button_text_color",
        "label": "Button text color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "steal_button_border_color",
        "label": "Button border color",
        "default": "#ff4747"
      },
      {
        "type": "header",
        "content": "Button hover colors"
      },
      {
        "type": "color",
        "id": "steal_button_hover_bg_color",
        "label": "Button hover background color",
        "default": "#e03232"
      },
      {
        "type": "color",
        "id": "steal_button_hover_text_color",
        "label": "Button hover text color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "steal_button_hover_border_color",
        "label": "Button hover border color",
        "default": "#e03232"
      },
      {
        "type": "header",
        "content": "Button border options"
      },
      {
        "type": "range",
        "id": "steal_button_border_radius",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Button border radius",
        "default": 4
      },
      {
        "type": "range",
        "id": "steal_button_border_width",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Button border thickness",
        "default": 1
      },
      {
        "type": "select",
        "id": "steal_button_size",
        "label": "Button size",
        "default": "medium",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ]
      },
      {
        "type": "select",
        "id": "steal_button_style",
        "label": "Button style",
        "default": "solid",
        "options": [
          {
            "value": "solid",
            "label": "Solid"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "rounded",
            "label": "Rounded"
          },
          {
            "value": "pill",
            "label": "Pill"
          }
        ]
      },
      {
        "type": "select",
        "id": "steal_button_animation",
        "label": "Button hover animation",
        "default": "lift",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "lift",
            "label": "Lift up"
          },
          {
            "value": "pulse",
            "label": "Pulse"
          },
          {
            "value": "shake",
            "label": "Shake"
          }
        ]
      },
      {
        "type": "header",
        "content": "4. Design options"
      },
      {
        "type": "select","id": "layout","default": "bee-container-wrap","label": "Layout",
        "options": [
            { "value": "bee-se-container", "label": "Container"},
            { "value": "stretch", "label": "Container stretch"},
            { "value": "bee-container-wrap", "label": "Wrapped container"},
            { "value": "bee-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info": "Margin top, margin right, margin bottom, margin left. If you do not use it please blank.",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design tablet options"
      },
      {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "placeholder": ",,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }
    ],
    "blocks": [
      {
        "type": "collection_item",
        "name": "Collection item",
        "settings": [
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Collection image"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Collection label",
            "info" :"Leave empty to use 'Collection name'"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Collection description",
            "info": "Only use for collection design 7. Show default if collecion has description"
          },
          {
            "type": "url",
            "id": "collection_link",
            "label": "Link (optional)",
            "info" :"Leave empty to use 'collection url'"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "Shop Now",
            "info": "Only use for collection design 2,6,7"
          },
          {
            "type": "select",
            "id": "content_pos",
            "label": "Content position",
            "info": "Only works with collection design 5",
            "default": "top_left",
            "options": [
                {
                    "label": "Top left",
                    "value": "top_left"
                },
                {
                    "label": "Top right",
                    "value": "top_right"
                },
                {
                    "label": "Bottom left",
                    "value": "bottom_left"
                }
            ]
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Collections list",
        "category": "group3",
        "blocks": [
          {"type": "collection_item"},
          {"type": "collection_item"},
          {"type": "collection_item"},
          {"type": "collection_item"}
        ]
      }
    ]
  }
{% endschema %}

