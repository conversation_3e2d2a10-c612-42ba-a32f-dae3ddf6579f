{%- liquid 
assign sid            = section.id
assign id_frm_login   = 'customer_login' |append:sid 
assign id_frm_recover = 'recover_customer_password' |append:sid 
assign id_frm_create  = 'customer_create' |append:sid -%}
{%- comment -%}
data-show="login" login , create, recover
{%- endcomment -%}

{%- if request.design_mode -%}
<link rel="stylesheet" href="{{ 'bee-login-sidebar.css' | asset_url }}" media="all">
<div id="bee-login-sidebar" class="bee-drawer bee-drawer__right{% if settings.login_popup_center %} bee-login-popup-true bee-drawer__bottom-to-top-lg{% endif %}" aria-hidden="true">
{%- endif -%}

   <div class="bee-drawer__header">
      <span class="is--login" aria-hidden="false">{{ 'customer.login.title' | t }}</span>
      <span class="is--recover" aria-hidden="true">{{ 'customer.recover_password.title' | t }}</span>
      <span class="is--create" aria-hidden="true">{{ 'customer.register.title' | t }}</span>
      <button class="bee-drawer__close" data-drawer-close aria-label="{{ 'customer.login.close' | t }}">
        {{ 'customer.login.close' | t }}
        <span class="bee-close-icon">
        <span></span>
        <span></span>
        </span>
   </div>
   <div class="bee-drawer__content" style="--item-rounded: {{ section.settings.item_radius }}px;">
      <div class="bee-drawer__main">
         <div data-bee-scroll-me class="bee-drawer__scroll bee-current-scrollbar"> 

            {%- comment -%}
             --------------------------------------------------------------------------------------------------------------------
             CUSTOMER RECOVERY FORM
             --------------------------------------------------------------------------------------------------------------------
             {%- endcomment -%}
            <div id="recover_{{sid}}" class="bee-content-login-sidebar is--recover" aria-hidden="true">
                <p class="bee-recover-note">{{ 'customer.recover_password.subtext' | t }}</p>
                {%- form 'recover_customer_password',id:id_frm_recover -%}
                  {%- assign recover_success = form.posted_successfully? -%}
                  <div class="bee_field bee-pr">
                    <label for="RecoverEmail">
                      {{ 'customer.recover_password.email' | t }}
                    </label>
                    <input type="email" class="bee_frm_input{% unless form.errors %} bee_mb_25{% endunless %}" 
                      value=""
                      name="email"
                      id="RecoverEmail"
                      autocorrect="off"
                      autocapitalize="off"
                      autocomplete="email"
                      {% if form.errors %}
                        aria-invalid="true"
                        aria-describedby="RecoverEmail-email-error"
                        autofocus
                      {% endif %}
                      placeholder="{{ 'customer.recover_password.email' | t }}"
                    >
                  </div>
                  {%- if form.errors -%}
                    <small id="RecoverEmail-email-error" class="form__message bee_mb_25">
                      <svg aria-hidden="true" focusable="false" role="presentation" viewBox="0 0 13 13">
                        <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                        <circle cx="6.5" cy="6.5" r="5.5" fill="var(--bee-error-color)" stroke="var(--bee-error-color)" stroke-width="0.7"/>
                        <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                        <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="var(--bee-error-color)" stroke-width="0.7">
                      </svg>
                      {{ form.errors.messages['form'] }}
                    </small>
                  {%- endif -%}
                  <button class="bee_btn_submmit bee_btn_black bee_mb_20">{{ 'customer.recover_password.submit' | t }}</button>
                  <div class="bee-login-footer bee-login-btn bee-text-center"><span>{{ 'customer.recover_password.cancel_title' | t }}</span><a class="bee-d-inline-block" data-login-sidebar="login" href="#login_{{sid}}">{{ 'customer.recover_password.cancel' | t }} </a></div>
                  
                {%- endform -%}
            </div>

             {%- comment -%}
             --------------------------------------------------------------------------------------------------------------------
             CUSTOMER LOGIN FORM
             --------------------------------------------------------------------------------------------------------------------
             {%- endcomment -%}
            <div id="login_{{sid}}" class="bee-content-login-sidebar is--login" aria-hidden="false">

                {%- form 'customer_login',id:id_frm_login, novalidate: 'novalidate' -%}
                  {%- comment -%}{%- if settings.return_login != blank -%}<input type="hidden" name="checkout_url" value="{{settings.return_login}}" />{%- endif -%}{%- endcomment -%}
                  <input type="hidden" name="checkout_url" value="{{ settings.return_login | default: routes.root_url }}" />
                  {%- if form.errors -%}
                    <h2 class="form__message" tabindex="-1" autofocus>
                      <span class="visually-hidden">{{ 'accessibility.error' | t }} </span>
                      <svg aria-hidden="true" focusable="false" role="presentation" viewBox="0 0 13 13">
                        <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                        <circle cx="6.5" cy="6.5" r="5.5" fill="var(--bee-error-color)" stroke="var(--bee-error-color)" stroke-width="0.7"/>
                        <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                        <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="var(--bee-error-color)" stroke-width="0.7">
                      </svg>
                      {{ 'customer.form.error_heading' | t }}
                    </h2>
                    {{ form.errors | default_errors }}
                  {%- endif -%}

                  <div class="bee_field bee-pr bee_mb_20">  
                    <label for="CustomerEmail">
                      {{ 'customer.login.email' | t }} <span class="required"></span>
                    </label>  
                    <input class="bee_frm_input" type="email" name="customer[email]" id="CustomerEmail" autocomplete="email" autocorrect="off" autocapitalize="off"
                      {% if form.errors contains 'form' %} aria-invalid="true" {% endif %} placeholder="{{ 'customer.login.email' | t }}" >
                  </div>

                  {%- if form.password_needed -%}
                    <div class="bee_field bee-pr bee_mb_25">
                      <label for="CustomerPassword">
                        {{ 'customer.login.password' | t }} <span class="required"></span>
                      </label> 
                      <input class="bee_frm_input" type="password" value="" name="customer[password]" id="CustomerPassword" autocomplete="current-password"
                        {% if form.errors contains 'form' %}
                          aria-invalid="true"
                        {% endif %}
                        placeholder="{{ 'customer.login.password' | t }}" >
                    </div>
                  {%- endif -%}

                  <button class="bee_btn_submmit bee_btn_black bee_mb_20">{{ 'customer.login.submit' | t }}</button>

                  <div class="bee-login-footer bee-lost-password bee-text-center"><span>{{ 'customer.login.lost_password' | t }}</span> <a href="#recover_{{sid}}" data-login-sidebar="recover" class="bee-d-block bee_mb_25 bee-text-start">{{ 'customer.login.forgot_password' | t }}</a></div>

                  <a class="bee-create-account bee-d-inline-block" data-login-sidebar="create" href="{{ routes.account_register_url }}"> {{ 'customer.login.create_account' | t }}</a>
                  
                {%- endform -%} 
            </div> 

             {%- comment -%}
             --------------------------------------------------------------------------------------------------------------------
             CUSTOMER CREATE FORM
             --------------------------------------------------------------------------------------------------------------------
             {%- endcomment -%}
             <div id="create_{{sid}}" class="bee-content-login-sidebar is--create" data-showing="create" aria-hidden="true">
               {%- form 'create_customer',id:id_frm_create, novalidate: 'novalidate',class:'bee-w-100' -%}
                  {%- if form.errors -%}
                  <h2 class="form__message" tabindex="-1" autofocus>
                    <svg aria-hidden="true" focusable="false" role="presentation">
                      <use href="#icon-error" />
                    </svg>
                    {{ 'customer.form.error_heading' | t }}
                  </h2>
                  <ul> 
                    {%- for field in form.errors -%}
                      <li>
                        {%- if field == 'form' -%}
                          {{ form.errors.messages[field] }}
                        {%- else -%}
                          <a href="#RegisterForm-{{ field }}">
                            {{ form.errors.translated_fields[field] | capitalize }}
                            {{ form.errors.messages[field] }}
                          </a>
                        {%- endif -%}
                      </li>
                    {%- endfor -%}
                  </ul>
                  {%- endif -%}
                  <div class="bee_field bee-pr bee_mb_20"> 
                    <label for="RegisterForm-FirstName">{{ 'customer.register.first_name' | t }}</label>     
                    <input class="bee_frm_input" type="text" name="customer[first_name]" id="RegisterForm-FirstName" {% if form.first_name %}value="{{ form.first_name }}"{% endif %} autocomplete="given-name" placeholder="{{ 'customer.register.first_name' | t }}">
                  </div>
                  <div class="bee_field bee-pr bee_mb_20">
                    <label for="RegisterForm-LastName">
                       {{ 'customer.register.last_name' | t }}
                    </label>
                    <input class="bee_frm_input" type="text" name="customer[last_name]" id="RegisterForm-LastName" {% if form.last_name %}value="{{ form.last_name }}"{% endif %} autocomplete="family-name" placeholder="{{ 'customer.register.last_name' | t }}">
                  </div>
                  <div class="bee_field bee-pr bee_mb_20">
                    <label for="RegisterForm-email">
                       {{ 'customer.register.email' | t }} <span class="required">*</span>
                    </label>
                    <input class="bee_frm_input" type="email" name="customer[email]" id="RegisterForm-email" {% if form.email %} value="{{ form.email }}"{% endif %} spellcheck="false" autocapitalize="off" autocomplete="email" aria-required="true" {% if form.errors contains 'email' %} aria-invalid="true" aria-describedby="RegisterForm-email-error" {% endif %} placeholder="{{ 'customer.register.email' | t }}">
                   </div>
                   {%- if form.errors contains 'email' -%}
                     <span id="RegisterForm-email-error" class="form__message">
                       <svg aria-hidden="true" focusable="false" role="presentation">
                         <use href="#icon-error" />
                       </svg>
                       {{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}.
                     </span>
                   {%- endif -%}
                   <div class="bee_field bee-pr bee_mb_20">     
                    <label for="RegisterForm-password">{{ 'customer.register.password' | t }} <span class="required">*</span></label>
                    <input class="bee_frm_input" type="password" name="customer[password]" id="RegisterForm-password" aria-required="true" {% if form.errors contains 'password' %} aria-invalid="true" aria-describedby="RegisterForm-password-error" {% endif %} placeholder="{{ 'customer.register.password' | t }}">
                   </div>
                   {%- if form.errors contains 'password' -%}
                     <span id="RegisterForm-password-error" class="form__message">
                       <svg aria-hidden="true" focusable="false" role="presentation">
                         <use href="#icon-error" />
                       </svg>
                       {{ form.errors.translated_fields['password'] | capitalize }} {{ form.errors.messages['password'] }}.
                     </span>
                   {%- endif -%}
                   <button class="bee_btn_submmit bee_btn_black bee_mb_20">{{ 'customer.register.submit' | t }}</button>

                    <div class="bee-login-footer bee-login-btn bee-text-center"><span>{{ 'customer.register.have_account' | t }}</span><a class="bee-d-inline-block" data-login-sidebar="login" href="{{ routes.account_login_url }}">  {{ 'customer.register.login_here' | t }}</a></div>

                   

               {%- endform -%}
            </div>

         </div>
      </div>
   </div>

{%- if request.design_mode %}</div>{% endif -%}

{% schema %}
  {
    "name": "Login Sidebar",
    "tag": "section",
    "class": "bee-section-admn2-fixed",
    "settings": [
      {
        "type": "range",
        "id": "item_radius",
        "label": "Button / input radius",
        "default": 4,
        "min": 0,
        "max": 60,
        "unit": "px"
      }
    ]
  }

{% endschema %}